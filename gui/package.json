{"name": "clipboard-sync-gui", "version": "1.0.0", "description": "macOS GUI for Clipboard Sync Ntfy", "main": "src/main/main.js", "homepage": "./", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev:react\" \"wait-on http://localhost:3000 && electron .\"", "dev:react": "cd src/renderer && npm start", "build": "npm run build:react && electron-builder", "build:react": "cd src/renderer && npm run build", "build:mac": "npm run build:react && electron-builder --mac", "dist": "npm run build", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.clipboardsync.app", "productName": "Clipboard Sync", "directories": {"output": "dist"}, "files": ["src/main/**/*", "src/renderer/build/**/*", "assets/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.utilities", "icon": "assets/icon.icns", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "entitlements": "assets/entitlements.mac.plist", "entitlementsInherit": "assets/entitlements.mac.plist", "hardenedRuntime": true, "gatekeeperAssess": false}, "dmg": {"title": "Clipboard Sync Installer", "icon": "assets/icon.icns", "background": "assets/dmg-background.png", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}, "devDependencies": {"concurrently": "^7.6.0", "electron": "^22.0.0", "electron-builder": "^23.6.0", "wait-on": "^7.0.1"}, "dependencies": {"electron-store": "^8.1.0", "electron-log": "^4.4.8", "js-yaml": "^4.1.0"}}