# Python
__pycache__/
*.py[cod]
*$py.class
*.so

# Environments
.env
.venv
venv/
env/
ENV/
pip-freeze.txt
pipenv-setup.lock

# Distribution / packaging
.eggs/
*.egg-info/
dist/
build/
*.egg
*.whl

# Config files (sensitive data might be stored here)
config/config.yaml

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDEs
.idea/
.vscode/
*.swp
*.swo

.DS_Store
clipboard_sync/__pycache__/__init__.cpython-313.pyc
clipboard_sync/__pycache__/clipboard_manager.cpython-313.pyc
clipboard_sync/__pycache__/ntfy_client.cpython-313.pyc
clipboard_sync/__pycache__/config.cpython-313.pyc
clipboard_sync/__pycache__/utils.cpython-313.pyc
clipboard_sync/__pycache__/sender.cpython-313.pyc
clipboard_sync/__pycache__/receiver.cpython-313.pyc
.aider*
